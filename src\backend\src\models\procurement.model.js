const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Procurement = sequelize.define('procurement', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true, // Made optional as requested
    references: {
      model: 'project',
      key: 'id'
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  supplierId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'supplier',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  // Tax-related pricing fields
  unitPriceExcludingTax: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '不含税单价'
  },
  unitPriceIncludingTax: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '含税单价'
  },
  taxRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '税率(%)'
  },
  taxAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '税额'
  },
  totalAmountExcludingTax: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '总金额(不含税)'
  },
  totalAmountIncludingTax: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '总金额(含税)'
  },
  // Legacy amount field for backward compatibility
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  // New fields for enhanced procurement management
  recipient: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '收货人'
  },
  expectedDeliveryDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '预计到货时间'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '附件列表'
  },
  invoiceType: {
    type: DataTypes.STRING,
    allowNull: true
  },
  procurementDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'completed'),
    defaultValue: 'pending'
  },
  returnInfo: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  returnBatch: {
    type: DataTypes.STRING,
    allowNull: true
  },
  returnInvoiceInfo: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  paymentSchedule: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

// Define associations
const setupAssociations = (models) => {
  const { Project, Supplier, User } = models;

  Procurement.belongsTo(Project, {
    foreignKey: 'projectId',
    as: 'project'
  });

  Procurement.belongsTo(Supplier, {
    foreignKey: 'supplierId',
    as: 'supplier'
  });

  Procurement.belongsTo(User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });
};

module.exports = { Procurement, setupAssociations };